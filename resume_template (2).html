<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简历 - 电子信息工程</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.4;
            padding: 20px;
            display: flex;
            justify-content: center;
            min-height: 100vh;
        }

        .resume-container {
            width: 210mm;
            max-width: 210mm;
            min-height: 297mm;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 8mm 20mm 20mm 20mm;
            position: relative;
        }

        /* 头部信息 */
        .header {
            position: relative;
            margin-bottom: 10px;
            padding: 5px 0 10px 0;
            border-bottom: 2px solid #1e3a8a;
        }

        .profile-photo {
            position: absolute;
            top: 0px;
            right: 0;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid #0080ff;
            background: #0080ff;
            flex-shrink: 0;
        }

        .profile-photo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transform-origin: center center;
            background: #0080ff;
        }

        .basic-info {
            margin-right: 110px;
        }

        .name {
            font-size: 22pt;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 5px;
        }

        .contact-info {
            font-size: 11pt;
            color: #555;
            line-height: 1.3;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px 20px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
            position: relative;
            padding-left: 20px;
        }

        .contact-item::before {
            position: absolute;
            left: 0;
            color: #1e3a8a;
            font-weight: bold;
        }

        .contact-item.phone::before {
            content: "📱";
        }

        .contact-item.email::before {
            content: "📧";
        }

        .contact-item.education::before {
            content: "🎓";
        }

        .contact-item.political::before {
            content: "🎖️";
        }

        .contact-item.github::before {
            content: "🔗";
        }

        /* 头像控制面板 */
        .avatar-controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(255,255,255,0.95);
            padding: 8px;
            border-radius: 5px;
            font-size: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 10;
        }

        .avatar-control-row {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-bottom: 5px;
        }

        .avatar-control-row label {
            min-width: 40px;
            font-size: 9px;
        }

        .avatar-control-row input[type="range"] {
            width: 80px;
        }

        .export-buttons {
            margin-top: 10px;
            display: flex;
            gap: 5px;
        }

        .export-btn {
            padding: 5px 10px;
            background: #1e3a8a;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 9px;
        }

        .export-btn:hover {
            background: #2563eb;
        }

        /* 求职意向 */
        .objective {
            margin-bottom: 12px;
            padding: 6px 12px;
            background-color: #f8fafc;
            border-left: 3px solid #1e3a8a;
            border-radius: 0 4px 4px 0;
        }

        .objective-content {
            font-size: 11pt;
            font-weight: 500;
            color: #1e3a8a;
        }

        /* 通用模块样式 */
        .section {
            margin-bottom: 14px;
        }

        .section-title {
            font-size: 13pt;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 8px;
            padding-bottom: 4px;
            border-bottom: 1px solid #e5e7eb;
        }

        /* 教育背景 */
        .education-item {
            margin-bottom: 15px;
        }

        .education-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin-bottom: 8px;
        }

        .school-info {
            font-size: 11pt;
            font-weight: bold;
            color: #333;
        }

        .time-period {
            font-size: 10pt;
            color: #666;
        }

        .education-details {
            font-size: 10pt;
            color: #555;
            line-height: 1.4;
        }

        .education-details p {
            margin-bottom: 3px;
        }

        .education-details strong {
            color: #333;
        }

        /* 项目经验 */
        .project-item {
            margin-bottom: 15px;
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin-bottom: 8px;
        }

        .project-title {
            font-size: 11pt;
            font-weight: bold;
            color: #333;
        }

        .project-role {
            font-size: 10pt;
            color: #666;
            font-style: italic;
        }

        .project-description {
            font-size: 10pt;
            color: #555;
            line-height: 1.4;
        }

        .project-description p {
            margin-bottom: 5px;
        }

        .project-description ul {
            margin: 5px 0;
            padding-left: 15px;
        }

        .project-description li {
            margin-bottom: 3px;
        }

        .project-description strong {
            color: #333;
            font-weight: 600;
        }

        /* 专业技能 */
        .skills-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .skill-category {
            margin-bottom: 10px;
        }

        .skill-category-title {
            font-size: 10pt;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .skill-list {
            font-size: 10pt;
            color: #555;
            line-height: 1.4;
        }

        /* 奖项荣誉 */
        .awards-list {
            font-size: 10pt;
            color: #555;
        }

        .award-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 3px 0;
        }

        .award-name {
            font-weight: 500;
            color: #333;
        }

        .award-time {
            color: #666;
            font-size: 9pt;
        }

        /* 打印样式 */
        @media print {
            @page {
                size: A4;
                margin: 10mm;
                /* 清除页眉页脚 */
                @top-left { content: ""; }
                @top-center { content: ""; }
                @top-right { content: ""; }
                @bottom-left { content: ""; }
                @bottom-center { content: ""; }
                @bottom-right { content: ""; }
            }

            body {
                background: white !important;
                padding: 0;
                margin: 0;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .resume-container {
                box-shadow: none !important;
                width: 100%;
                min-height: auto;
                padding: 5mm 10mm;
                margin: 0;
            }

            .avatar-controls {
                display: none !important;
            }

            /* 强制隐藏浏览器默认的页眉页脚 */
            html {
                margin: 0 !important;
                padding: 0 !important;
            }
        }

        /* 响应式调整 */
        @media screen and (max-width: 768px) {
            .resume-container {
                width: 100%;
                padding: 15px;
            }

            .profile-section {
                flex-direction: column;
                gap: 10px;
            }

            .basic-info {
                text-align: center;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 头像控制面板 -->
        <div class="avatar-controls">
            <div class="avatar-control-row">
                <label for="avatarScaleSlider">缩放:</label>
                <input type="range" id="avatarScaleSlider" min="0.5" max="2.0" step="0.05" value="1">
            </div>
            <div class="avatar-control-row">
                <label for="avatarPanXSlider">左右:</label>
                <input type="range" id="avatarPanXSlider" min="-50" max="50" step="1" value="0">
            </div>
            <div class="avatar-control-row">
                <label for="avatarPanYSlider">上下:</label>
                <input type="range" id="avatarPanYSlider" min="-50" max="50" step="1" value="0">
            </div>
            <div class="export-buttons">
                <button id="downloadPdfButton" class="export-btn">PDF</button>
                <button id="printButton" class="export-btn">打印</button>
            </div>
        </div>

        <!-- 头部信息 -->
        <header class="header">
            <div class="profile-photo">
                <img id="avatarImage" src="imagesmy_photo.jpg" alt="个人头像">
            </div>
            <div class="basic-info">
                <h1 class="name">张汉权</h1>
                <div class="contact-info">
                    <div class="contact-item">  13235651045</div>
                    <div class="contact-item">✉️ <EMAIL></div>
                    <div class="contact-item">🎓 XX大学 电子信息工程</div>
                    <div class="contact-item"> ️ 预备党员</div>
                    <div class="contact-item">  GitHub: https://github.com/zc217888/LabGuardian-STM32</div>
                </div>
            </div>
        </header>

        <!-- 求职意向 -->
        <section class="objective">
            <div class="objective-content">求职意向：嵌入式软件开发工程师 / 硬件设计工程师</div>
        </section>

        <!-- 教育背景 -->
        <section class="section">
            <h2 class="section-title">教育背景</h2>
            <div class="education-item">
                <div class="education-header">
                    <div class="school-info">XX大学 · 电子信息工程学院 · 电子信息工程专业 · 本科</div>
                    <div class="time-period">2021.09 - 2025.06</div>
                </div>
                <div class="education-details">
                    <p><strong>GPA：</strong>3.6/4.0 (专业排名：前15%)</p>
                    <p><strong>核心课程：</strong>数字电路(95)、模拟电路(92)、单片机原理(94)、信号与系统(90)、通信原理(88)、C/C++程序设计(93)、嵌入式系统(91)</p>
                    <p><strong>荣誉奖项：</strong>国家励志奖学金、校级一等奖学金(2次)、三好学生</p>
                </div>
            </div>
        </section>

        <!-- 项目经验 -->
        <section class="section">
            <h2 class="section-title">项目经验</h2>

            <div class="project-item">
                <div class="project-header">
                    <div class="project-title">基于STM32的多节点环境监测系统</div>
                    <div class="project-role">技术负责人 | 2023.06-2023.07</div>
                </div>
                <div class="project-description">
                    <p><strong>项目背景：</strong>为解决实验室环境参数人工巡检效率低、数据记录不连续问题，设计分布式实时监测系统，实现温湿度、CO₂、光照的自动化采集、传输与可视化报警</p>
                    <ul>
                        <li><strong>核心职责：</strong>作为3人团队技术负责人，独立完成STM32嵌入式固件全开发、Qt上位机架构设计与全模块开发，主导系统集成测试与性能优化</li>
                        <li><strong>硬件开发：</strong>基于STM32F407，用C语言开发DHT11（单总线）、SGP30（I2C）传感器驱动，通过示波器调试优化时序参数，将数据读取成功率从60%提升至95%</li>
                        <li><strong>上位机开发：</strong>用Qt5实现串口通信（UART）、JSON数据解析与实时图表（Qt Charts），设计双缓冲机制解决数据粘包问题，传输成功率达99.2%</li>
                        <li><strong>性能优化：</strong>通过51点滑动窗口缓存控制内存占用，多线程信号槽机制确保上位机刷新延迟<200ms，异常处理机制支持72小时无崩溃运行</li>
                        <li><strong>项目成果：</strong>实现4类参数同步监测，减少90%人工巡检量；获全国大学生嵌入式芯片与系统设计竞赛省级二等奖；代码量4000+行（嵌入式1200行+Qt 2800行），已上传至GitHub</li>
                    </ul>
                </div>
            </div>

            <div class="project-item">
                <div class="project-header">
                    <div class="project-title">基于GEC6818的智能多媒体交互控制系统</div>
                    <div class="project-role">负责人兼独立开发者 | 2024.12-2025.01</div>
                </div>
                <div class="project-description">
                    <p><strong>项目背景：</strong>针对传统嵌入式设备功能单一、交互体验差的问题，设计集成触摸控制、语音/人脸识别及多媒体播放的智能家居控制原型，覆盖9大功能界面</p>
                    <ul>
                        <li><strong>核心工作：</strong>主导系统架构设计（多进程协作+状态机框架），独立开发8大模块（含3694行主控程序、1988行相册管理程序），编写LCD/触摸屏驱动，集成OpenCV人脸识别（准确率85%+）与讯飞语音SDK</li>
                        <li><strong>技术实现：</strong>用C语言操作framebuffer实现无依赖图形显示，Python开发AI模块；通过FIFO缓存策略将内存占用降低40%，触摸响应从100ms优化至30ms，解决多进程资源竞争问题</li>
                        <li><strong>成果指标：</strong>总代码量8000+行，集成8个硬件模块及2个AI功能，系统稳定运行24小时无崩溃，获课程设计优秀项目，技术创新点（无依赖图形引擎）被纳入实验室案例库</li>
                    </ul>
                </div>
            </div>

            <div class="project-item">
                <div class="project-header">
                    <div class="project-title">基于STM32F407的智能车载电子系统与多传感器融合控制平台</div>
                    <div class="project-role">项目技术负责人兼首席开发工程师 | 2024.03-2024.07</div>
                </div>
                <div class="project-description">
                    <p><strong>背景与目标：</strong>针对传统汽车电子平台功能单一问题，设计集成多传感器融合、自适应控制及车载诊断的智能系统，为汽车电子教学提供先进实训方案</p>
                    <ul>
                        <li><strong>核心职责：</strong>主导系统架构设计，移植FreeRTOS实现多任务调度，开发卡尔曼滤波传感器融合与自适应PID算法，完成UDS车载诊断协议栈开发及系统集成测试</li>
                        <li><strong>技术实现：</strong>基于STM32F407芯片，采用C语言在Keil MDK开发环境中实现12,500行代码；通过FreeRTOS构建8个并发任务（传感器融合、安全监控等），集成MPU6050/超声波等传感器，用卡尔曼滤波将定位精度提升至±2cm，自适应PID使电机控制精度达±0.5RPM</li>
                        <li><strong>量化成果：</strong>系统响应时间从50ms优化至15ms，通信成功率提升至99.95%，获课程设计优秀项目</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 专业技能 -->
        <section class="section">
            <h2 class="section-title">专业技能</h2>
            <div class="skills-grid">
                <div class="skill-category">
                    <div class="skill-category-title">编程语言</div>
                    <div class="skill-list">
                        • C/C++ (熟练，嵌入式开发)<br>
                        • Verilog HDL (掌握，FPGA设计)<br>
                        • Python (掌握，数据处理)<br>
                        • MATLAB (熟悉，算法仿真)
                    </div>
                </div>

                <div class="skill-category">
                    <div class="skill-category-title">硬件平台</div>
                    <div class="skill-list">
                        • STM32系列 (熟练使用)<br>
                        • Xilinx FPGA (Zynq-7000)<br>
                        • Arduino/ESP32 (项目经验)<br>
                        • TI DSP (了解基础)
                    </div>
                </div>

                <div class="skill-category">
                    <div class="skill-category-title">开发工具</div>
                    <div class="skill-list">
                        • Altium Designer (PCB设计)<br>
                        • Keil MDK/IAR (嵌入式开发)<br>
                        • Xilinx Vivado (FPGA开发)<br>
                        • Proteus/Multisim (电路仿真)
                    </div>
                </div>

                <div class="skill-category">
                    <div class="skill-category-title">通信协议</div>
                    <div class="skill-list">
                        • UART/SPI/I2C (熟练)<br>
                        • CAN/Modbus (掌握)<br>
                        • TCP/IP/WiFi (项目应用)<br>
                        • USB/Ethernet (了解)
                    </div>
                </div>
            </div>
        </section>

        <!-- 奖项荣誉 -->
        <section class="section">
            <h2 class="section-title">奖项荣誉</h2>
            <div class="awards-list">
                <div class="award-item">
                    <span class="award-name">全国大学生电子设计竞赛 省级三等奖</span>
                    <span class="award-time">2024.08</span>
                </div>
                <div class="award-item">
                    <span class="award-name">全国大学生智能车竞赛 校级一等奖</span>
                    <span class="award-time">2023.07</span>
                </div>
                <div class="award-item">
                    <span class="award-name">校级科技创新竞赛 二等奖</span>
                    <span class="award-time">2024.06</span>
                </div>
                <div class="award-item">
                    <span class="award-name">国家励志奖学金</span>
                    <span class="award-time">2023.12</span>
                </div>
                <div class="award-item">
                    <span class="award-name">校级一等奖学金 (连续两年)</span>
                    <span class="award-time">2022-2024</span>
                </div>
            </div>
        </section>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 头像控制功能
            const avatarImage = document.getElementById('avatarImage');
            const avatarScaleSlider = document.getElementById('avatarScaleSlider');
            const avatarPanXSlider = document.getElementById('avatarPanXSlider');
            const avatarPanYSlider = document.getElementById('avatarPanYSlider');

            let currentScale = 1;
            let currentPanX = 0;
            let currentPanY = 0;

            function updateAvatarTransform() {
                if (avatarImage) {
                    avatarImage.style.transform = `translateX(${currentPanX}px) translateY(${currentPanY}px) scale(${currentScale})`;
                }
            }

            if (avatarImage && avatarScaleSlider && avatarPanXSlider && avatarPanYSlider) {
                currentScale = parseFloat(avatarScaleSlider.value);
                currentPanX = parseInt(avatarPanXSlider.value);
                currentPanY = parseInt(avatarPanYSlider.value);
                updateAvatarTransform();

                avatarScaleSlider.addEventListener('input', function() {
                    currentScale = parseFloat(this.value);
                    updateAvatarTransform();
                });

                avatarPanXSlider.addEventListener('input', function() {
                    currentPanX = parseInt(this.value);
                    updateAvatarTransform();
                });

                avatarPanYSlider.addEventListener('input', function() {
                    currentPanY = parseInt(this.value);
                    updateAvatarTransform();
                });
            }

            // PDF导出功能
            const downloadPdfButton = document.getElementById('downloadPdfButton');
            if (downloadPdfButton) {
                downloadPdfButton.addEventListener('click', async function() {
                    try {
                        // 隐藏控制面板
                        const controls = document.querySelectorAll('.avatar-controls, .export-buttons');
                        controls.forEach(control => control.style.display = 'none');

                        // 等待一下确保样式应用
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // 使用html2canvas截图
                        const canvas = await html2canvas(document.querySelector('.resume-container'), {
                            scale: 2,
                            useCORS: true,
                            allowTaint: true,
                            backgroundColor: '#ffffff',
                            width: 794, // A4宽度 (210mm * 3.78)
                            height: 1123 // A4高度 (297mm * 3.78)
                        });

                        // 创建PDF
                        const { jsPDF } = window.jspdf;
                        const pdf = new jsPDF('p', 'mm', 'a4');

                        const imgData = canvas.toDataURL('image/png');
                        pdf.addImage(imgData, 'PNG', 0, 0, 210, 297);

                        // 下载PDF
                        pdf.save('个人简历.pdf');

                        // 恢复控制面板
                        controls.forEach(control => control.style.display = '');
                    } catch (error) {
                        console.error('PDF导出失败:', error);
                        alert('PDF导出失败，请检查浏览器兼容性');

                        // 恢复控制面板
                        const controls = document.querySelectorAll('.avatar-controls, .export-buttons');
                        controls.forEach(control => control.style.display = '');
                    }
                });
            }

            // 打印功能
            const printButton = document.getElementById('printButton');
            if (printButton) {
                printButton.addEventListener('click', function() {
                    window.print();
                });
            }
        });
    </script>
</body>
</html>
